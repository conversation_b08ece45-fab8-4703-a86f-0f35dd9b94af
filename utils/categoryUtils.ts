import { CategoryItem, getCurrentUserGender, getGenderSpecificCategoriesFromBackend } from '@/data/categories';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import Meteor from '@meteorrn/core';

/**
 * Gets the appropriate categories based on the user's gender
 * If gender is not available from the current user, it will try to fetch it from the server
 * Now uses the backend API instead of hardcoded categories
 */
export async function getGenderSpecificCategories(): Promise<CategoryItem[]> {
  // First try to get gender from the current user
  let gender = getCurrentUserGender();

  // If gender is not available, try to fetch it from the server
  if (!gender && Meteor.userId()) {
    try {
      // Use a promise to make the Meteor call async
      const userProfile = await new Promise((resolve, reject) => {
        Meteor.call('users-getUser', (err: any, res: any) => {
          if (err) {
            console.error('Error fetching user data:', err);
            reject(err);
            return;
          }
          resolve(res);
        });
      });

      // Extract gender from the response
      if (userProfile && userProfile.data && userProfile.data.profile && userProfile.data.profile.gender) {
        gender = userProfile.data.profile.gender;
        console.log('Gender from server call:', gender);
      }
    } catch (error) {
      console.error('Error fetching user gender from server:', error);
    }
  }

  // Log the gender we're using
  console.log('Using gender for categories:', gender || 'Not available (using default)');

  // If we have a gender, fetch categories from backend
  if (gender) {
    return await fetchCategoriesFromBackend(gender);
  }

  // Default empty categories if no gender is available
  return [];
}

/**
 * Gets a simplified list of category names for UI display
 * This is useful for dropdown menus or filter buttons
 */
export function getSimpleCategoryNames(categories: CategoryItem[]): string[] {
  // Add "All" as the first option
  const categoryNames = ['All'];

  // Add top-level category names - handle both old 'name' and new 'mainCategory' fields
  categories.forEach(category => {
    const categoryName = category.mainCategory || category.name;
    if (categoryName) {
      categoryNames.push(categoryName);
    }
  });

  return categoryNames;
}

/**
 * Maps a category name to its ID
 * Useful when you have a category name from UI and need to find its ID
 */
export function getCategoryIdByName(categoryName: string, categories: CategoryItem[]): string | null {
  // Handle "All" category
  if (categoryName === 'All') {
    return 'all';
  }

  // Find the category with the matching name - handle both old 'name' and new 'mainCategory' fields
  const category = categories.find(cat =>
    (cat.mainCategory || cat.name) === categoryName
  );
  return category ? category.id : null;
}

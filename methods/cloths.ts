import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Meteor from '@meteorrn/core';

type CategoriesResponse = {
  success: boolean;
  data: any;
};

type ImageUploadResponse = {
  success: boolean;
  data: any;
  [key: string]: any;
};

export const getCategories = () =>
  useQuery({
    queryKey: ['categories'],
    queryFn: () => {
      return new Promise<CategoriesResponse>((resolve, reject) => {
        console.log('🔍 CATEGORIES API (getCategories) - Fetching categories without gender filter');

        Meteor.call(
          'itemCategories-fetch',
          (err: any, res: CategoriesResponse) => {
            if (err) {
              console.error('❌ CATEGORIES API ERROR (getCategories) - Error fetching categories:', err);
              reject(err);
              return;
            }

            // Log the complete API response
            console.log('📦 CATEGORIES API RESPONSE (getCategories) - Full response object:', JSON.stringify(res, null, 2));
            console.log('📋 CATEGORIES API RESPONSE (getCategories) - Response structure:', Object.keys(res).map(key => `${key}: ${typeof res[key]}`));

            // Ensure we never resolve with undefined
            if (!res || !res.data) {
              console.log('⚠️ CATEGORIES API (getCategories) - No data in response, returning empty array');
              resolve({
                success: true,
                data: { itemCategories: [] }
              });
              return;
            }

            // Log categories data details
            if (res.data.itemCategories) {
              console.log('📊 CATEGORIES API DATA (getCategories) - Total categories count:', res.data.itemCategories.length);
              console.log('📊 CATEGORIES API DATA (getCategories) - Categories array:', JSON.stringify(res.data.itemCategories, null, 2));

              // Check for duplicates - handle both old 'name' and new 'mainCategory' fields
              const categoryNames = res.data.itemCategories.map((cat: any) =>
                cat.mainCategory || cat.name || cat.categoryName
              );
              const duplicateNames = categoryNames.filter((name: string, index: number) =>
                categoryNames.indexOf(name) !== index
              );

              if (duplicateNames.length > 0) {
                console.warn('⚠️ CATEGORIES API DUPLICATES (getCategories) - Found duplicate category names:', duplicateNames);
              } else {
                console.log('✅ CATEGORIES API (getCategories) - No duplicate category names found');
              }

              // Log the field structure to understand the backend changes
              if (res.data.itemCategories.length > 0) {
                const firstCategory = res.data.itemCategories[0];
                console.log('📋 CATEGORIES API SCHEMA (getCategories) - First category structure:', Object.keys(firstCategory));
                console.log('📋 CATEGORIES API SCHEMA (getCategories) - Has mainCategory field:', !!firstCategory.mainCategory);
                console.log('📋 CATEGORIES API SCHEMA (getCategories) - Has name field:', !!firstCategory.name);
              }
            }

            console.log('✅ CATEGORIES API (getCategories) - Categories fetched successfully');
            resolve(res.data);
          },
        );
      });
    },
  });

// Helper function to process clothes data consistently
function processClothesData(data: any) {
  if (!data) {
    return { items: [] };
  }

  if (!data.items) {
    return { items: [] };
  }

  if (!Array.isArray(data.items)) {
    return { items: [] };
  }

  // Filter out test items
  const filteredItems = data.items.filter((item: any) => {
    if (!item) {
      return false;
    }
    return item.name !== 'test' && item.name !== 'test1';
  });

  // Process image URLs
  const processedItems = filteredItems.map((item: any) => {
    // Ensure imageUrl is properly formatted
    if (item.imageUrl && !item.imageUrl.startsWith('http')) {
      item.imageUrl = `https://myuse.s3.ap-southeast-1.amazonaws.com/${item.imageUrl}`;
    }
    return item;
  });

  return { ...data, items: processedItems };
}

export const getClothes = (categoryName?: string) =>
  useQuery({
    queryKey: ['clothes', categoryName || 'all'], // Standardize the key
    queryFn: () => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        console.log('🔍 CLOTHES API - Fetching clothes with categoryName:', categoryName);

        Meteor.call(
          'items-fetchAll',
          { categoryName },
          (err: any, res: ImageUploadResponse) => {
            if (err) {
              console.error('❌ CLOTHES API ERROR - Error fetching clothes:', err);
              reject(err);
              return;
            }

            // Log the complete API response
            console.log('📦 CLOTHES API RESPONSE - Full response object:', JSON.stringify(res, null, 2));
            console.log('📋 CLOTHES API RESPONSE - Response structure:', Object.keys(res).map(key => `${key}: ${typeof res[key]}`));

            if (!res.data) {
              console.log('⚠️ CLOTHES API - No data in response, returning empty array');
              resolve({
                success: true,
                data: { items: [] }
              });
              return;
            }

            if (!res.data.items || !Array.isArray(res.data.items)) {
              console.log('⚠️ CLOTHES API - No items array in response data, returning empty array');
              console.log('📊 CLOTHES API DATA - res.data structure:', Object.keys(res.data || {}));
              resolve({
                success: true,
                data: { items: [] }
              });
              return;
            }

            console.log('📊 CLOTHES API DATA - Total items count:', res.data.items.length);
            console.log('📊 CLOTHES API DATA - Items array:', JSON.stringify(res.data.items, null, 2));

            // Check for duplicates in clothes data
            const itemIds = res.data.items.map((item: any) => item._id).filter(Boolean);
            const duplicateIds = itemIds.filter((id: string, index: number) => itemIds.indexOf(id) !== index);

            if (duplicateIds.length > 0) {
              console.warn('⚠️ CLOTHES API DUPLICATES - Found duplicate item IDs:', duplicateIds);
            } else {
              console.log('✅ CLOTHES API - No duplicate item IDs found');
            }

            // Process data consistently here
            const processedData = processClothesData(res.data);
            console.log('🔄 CLOTHES API PROCESSED - Processed data:', JSON.stringify(processedData, null, 2));
            resolve(processedData);
          },
        );
      });
    },
    // Increase staleTime to reduce unnecessary refetches
    staleTime: 5 * 60 * 1000, // 5 minutes
    // Remove automatic refetchInterval to prevent excessive API calls
    // refetchInterval: 60 * 1000, // 1 minute
    // Only refetch when the component mounts
    refetchOnMount: true,
    // Don't refetch on window focus to prevent excessive API calls
    refetchOnWindowFocus: false
  });

export const addItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (item: any) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call('items-add', item, (err: any, res: ImageUploadResponse) => {
          if (err) {
            reject(err);
            return;
          }

          // Check if res.data exists and has the expected structure
          if (res && res.data) {
            resolve(res.data);
          } else {
            resolve(res);
          }
        });
      });
    },
    onSuccess: () => {
      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

export const ItemsUpdate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (item: any) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call(
          'items-update',
          item,
          (err: any, res: ImageUploadResponse) => {
            if (err) {
              reject(err);
              return;
            }

            resolve(res.data);
          },
        );
      });
    },
    onSuccess: () => {
      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

//   const handleUploadFile = async () => {
//     const file = document.getElementById('file-input').files[0];
//     const params = {
//             fileName: file?.name,
//             fileType: file?.type,
//             folderPath: 'avatars'
//     };
//     const result = await Meteor.callAsync("AWS-generatePreSignedUrl", params);
//     const {
//             signedURL,
//             fileURL // This is the file URL once upload is successful
//     } = result?.data;
//     fetch(signedURL, {
//             method: 'PUT',
//             body: file,
//             headers: {'Content-Type': file?.type}
//     })
//         .then(response => {
//                 if (response.ok) {
//                         console.log('File uploaded successfully', response);
//                         // Todo: Save fileURL in DB
//                 } else {
//                         console.error('Failed to upload file', response);
//                 }
//         })
//         .catch(error => {
//                 console.error('Error uploading file', error);
//         });
// };

export const UploadImage = () => {
  return useMutation({
    mutationFn: ({
      // imageUrl is unused but kept for type compatibility
      fileName,
      fileType,
      folderPath,
    }: {
      imageUrl: string;
      fileName: string;
      fileType: string;
      folderPath: string;
    }) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call(
          'AWS-generatePreSignedUrl',
          {
            fileName,
            fileType,
            folderPath,
          },
          (err: any, res: ImageUploadResponse) => {
            if (err) {
              reject(err);
              return;
            }

            resolve(res.data);
          },
        );
      });
    },
    onSuccess: () => {
      // Success handling is done by the component
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

const base64ToBlob = async (encoded: string) => {
  // check if base64 already has data:image/jpg;base64 prefix
  if (encoded.startsWith('data:image/jpg;base64,')) {
    let url = `${encoded}`;
    let res = await fetch(url);
    let blob = await res?.blob();
    return blob;
  }

  let url = `data:image/jpg;base64,${encoded}`;
  let res = await fetch(url);
  let blob = await res?.blob();
  return blob;
};

export const uploadImageToS3 = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      imageUrl,
      preSignedUrl,
    }: {
      imageUrl: string;
      preSignedUrl: string;
    }) => {
      return new Promise<ImageUploadResponse>(async (resolve, reject) => {
        try {
          const file = await base64ToBlob(imageUrl);

        fetch(preSignedUrl, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': 'image/jpeg',
          },
        }).then((response) => {
          if (response.ok) {
            // Create a proper response object that matches ImageUploadResponse
            resolve({
              success: true,
              data: { url: preSignedUrl }
            });
          } else {
            reject(response);
          }
        })
          .catch((error) => {
            reject(error);
          });
        } catch (error) {
          reject(error);
        }
      });
    },
    onSuccess: () => {
      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

// Delete an item by ID
export const deleteItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (itemId: string) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call('items-delete', { itemId }, (err: any, res: ImageUploadResponse) => {
          if (err) {
            reject(err);
            return;
          }

          resolve(res);
        });
      });
    },
    onSuccess: () => {
      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

//update item
export const updateItem = () =>
  useMutation({
    mutationFn: (item: any) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call('items-update', item, (err: any, res: ImageUploadResponse) => {
          if (err) {
            reject(err);
            return;
          }

          resolve(res.data);
        });
      });
    },
  });
